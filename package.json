{"name": "snipco", "version": "0.0.1", "description": "A tool that turns code into beautiful images for social sharing.", "keywords": ["generate", "export", "share", "snippet", "image", "code", "yumma<PERSON>s"], "homepage": "https://snipco.netlify.app", "private": true, "scripts": {"astro": "astro", "build": "astro build", "dev": "astro dev", "format": "prettier --write .", "preview": "astro preview"}, "dependencies": {"@expressive-code/plugin-line-numbers": "^0.41.3", "@ianvs/prettier-plugin-sort-imports": "^4.6.1", "astro": "^5.12.9", "astro-expressive-code": "^0.41.3"}, "devDependencies": {"prettier": "^3.6.2", "yummacss": "^3.1.0"}}