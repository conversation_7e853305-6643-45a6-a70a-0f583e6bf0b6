// @ts-check
import { defineConfig } from 'astro/config'
import astroExpressiveCode, { ExpressiveCodeTheme } from 'astro-expressive-code'
import fs from 'node:fs'

const jsoncString = fs.readFileSync(new URL(`./midnight.json`, import.meta.url), 'utf-8')
const midnight = ExpressiveCodeTheme.fromJSONString(jsoncString)

export default defineConfig({
  integrations: [
    astroExpressiveCode({
      themes: [midnight],
      styleOverrides: {
        borderRadius: '0',
        frames: {
          shadowColor: 'transparent'
        },
        textMarkers: {
          delBackground: "hsla(0, 48%, 77%, 0.100)",
          delHue: "hsl(0, 48%, 77%)",
          insBackground: "hsla(127, 48%, 77%, 0.150)",
          insHue: "hsl(127, 48%, 77%)",
          markBackground: "hsla(231, 73%, 77%, 0.100)",
          markHue: "hsl(231, 73%, 77%)",
          },
      }
    }),
  ],

  devToolbar: {
    enabled: false,
  },
});